#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BiliNote 项目一键启动脚本
自动启动后端 FastAPI 服务和前端 Vite 开发服务器
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class Color:
    """终端颜色类"""
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(text, color=Color.WHITE):
    """打印彩色文本"""
    print(f"{color}{text}{Color.END}")

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                        BiliNote 启动器                       ║
    ║                   一键启动前后端开发环境                       ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print_colored(banner, Color.CYAN)

class ProjectStarter:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "BillNote_frontend"
        self.backend_process = None
        self.frontend_process = None
        
    def check_prerequisites(self):
        """检查前置条件"""
        print_colored("🔍 检查前置条件...", Color.YELLOW)
        
        # 检查Python
        try:
            python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
            print_colored(f"✅ Python: {python_version}", Color.GREEN)
        except Exception as e:
            print_colored(f"❌ Python 检查失败: {e}", Color.RED)
            return False
            
        # 检查Node.js
        try:
            node_version = subprocess.check_output(["node", "--version"], text=True).strip()
            print_colored(f"✅ Node.js: {node_version}", Color.GREEN)
        except Exception as e:
            print_colored(f"❌ Node.js 未安装或不在PATH中: {e}", Color.RED)
            print_colored("请安装 Node.js: https://nodejs.org/", Color.YELLOW)
            return False
            
        # 检查npm
        try:
            npm_version = subprocess.check_output(["npm", "--version"], text=True).strip()
            print_colored(f"✅ npm: {npm_version}", Color.GREEN)
        except Exception as e:
            print_colored(f"❌ npm 检查失败: {e}", Color.RED)
            return False
            
        # 检查FFmpeg
        try:
            ffmpeg_version = subprocess.check_output(["ffmpeg", "-version"], text=True).split('\n')[0]
            print_colored(f"✅ FFmpeg: {ffmpeg_version}", Color.GREEN)
        except Exception as e:
            print_colored("❌ FFmpeg 未安装或不在PATH中", Color.RED)
            print_colored("BiliNote 需要 FFmpeg 来处理音视频文件", Color.YELLOW)
            print_colored("请先安装 FFmpeg:", Color.CYAN)
            print_colored("  macOS: brew install ffmpeg", Color.WHITE)
            print_colored("  Ubuntu/Debian: sudo apt install ffmpeg", Color.WHITE)
            print_colored("  Windows: https://www.gyan.dev/ffmpeg/builds/", Color.WHITE)
            print_colored("  官方下载: https://ffmpeg.org/download.html", Color.WHITE)
            return False
            
        # 检查目录结构
        if not self.backend_dir.exists():
            print_colored(f"❌ 后端目录不存在: {self.backend_dir}", Color.RED)
            return False
        
        if not self.frontend_dir.exists():
            print_colored(f"❌ 前端目录不存在: {self.frontend_dir}", Color.RED)
            return False
        
        # 检查并创建 .env 文件
        env_file = self.project_root / ".env"
        if not env_file.exists():
            print_colored("⚠️ .env 文件不存在，创建默认配置...", Color.YELLOW)
            try:
                default_env_content = """# BiliNote 环境配置
# 后端配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000

# 静态文件配置
STATIC=/static
OUT_DIR=./static/screenshots

# 转录器配置
TRANSCRIBER_TYPE=fast-whisper

# GPT 配置 (请根据需要配置)
# OPENAI_API_KEY=your_openai_api_key
# OPENAI_BASE_URL=https://api.openai.com/v1

# DeepSeek 配置 (请根据需要配置)
# DEEPSEEK_API_KEY=your_deepseek_api_key
# DEEPSEEK_BASE_URL=https://api.deepseek.com

# 千问配置 (请根据需要配置)
# QWEN_API_KEY=your_qwen_api_key
# QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
"""
                env_file.write_text(default_env_content, encoding='utf-8')
                print_colored("✅ 已创建默认 .env 文件", Color.GREEN)
                print_colored("💡 请根据需要编辑 .env 文件配置 API Key", Color.CYAN)
            except Exception as e:
                print_colored(f"❌ 创建 .env 文件失败: {e}", Color.RED)
                return False
        else:
            print_colored("✅ .env 文件已存在", Color.GREEN)
            
        print_colored("✅ 前置条件检查通过", Color.GREEN)
        return True
    
    def install_backend_dependencies(self):
        """安装后端依赖"""
        print_colored("📦 检查后端依赖...", Color.YELLOW)
        
        requirements_file = self.backend_dir / "requirements.txt"
        if not requirements_file.exists():
            print_colored(f"❌ requirements.txt 不存在: {requirements_file}", Color.RED)
            return False
            
        try:
            # 检查是否需要安装依赖
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ], cwd=self.backend_dir, capture_output=True, text=True)
            
            if result.returncode != 0:
                print_colored(f"❌ 后端依赖安装失败: {result.stderr}", Color.RED)
                return False
                
            print_colored("✅ 后端依赖已就绪", Color.GREEN)
            return True
            
        except Exception as e:
            print_colored(f"❌ 后端依赖安装异常: {e}", Color.RED)
            return False
    
    def install_frontend_dependencies(self):
        """安装前端依赖"""
        print_colored("📦 检查前端依赖...", Color.YELLOW)
        
        package_json = self.frontend_dir / "package.json"
        if not package_json.exists():
            print_colored(f"❌ package.json 不存在: {package_json}", Color.RED)
            return False
            
        node_modules = self.frontend_dir / "node_modules"
        if node_modules.exists():
            print_colored("✅ 前端依赖已安装", Color.GREEN)
            return True
            
        try:
            print_colored("正在安装前端依赖...", Color.YELLOW)
            result = subprocess.run([
                "npm", "install"
            ], cwd=self.frontend_dir, capture_output=True, text=True)
            
            if result.returncode != 0:
                print_colored(f"❌ 前端依赖安装失败: {result.stderr}", Color.RED)
                return False
                
            print_colored("✅ 前端依赖安装完成", Color.GREEN)
            return True
            
        except Exception as e:
            print_colored(f"❌ 前端依赖安装异常: {e}", Color.RED)
            return False
    
    def start_backend(self):
        """启动后端服务"""
        print_colored("🚀 启动后端服务...", Color.YELLOW)
        
        main_py = self.backend_dir / "main.py"
        if not main_py.exists():
            print_colored(f"❌ main.py 不存在: {main_py}", Color.RED)
            return False
            
        try:
            # 不捕获输出，让后端错误信息直接显示
            self.backend_process = subprocess.Popen([
                sys.executable, "main.py"
            ], cwd=self.backend_dir)
            
            # 等待一下让后端启动
            time.sleep(5)
            
            # 检查进程是否还在运行
            if self.backend_process.poll() is None:
                print_colored("✅ 后端服务启动成功 (http://localhost:8000)", Color.GREEN)
                return True
            else:
                print_colored(f"❌ 后端服务启动失败，进程退出码: {self.backend_process.returncode}", Color.RED)
                print_colored("请检查上方的错误信息", Color.YELLOW)
                
                # 尝试手动启动来查看错误
                print_colored("🔍 尝试手动启动后端以查看详细错误...", Color.YELLOW)
                manual_result = subprocess.run([
                    sys.executable, "main.py"
                ], cwd=self.backend_dir, capture_output=True, text=True, timeout=10)
                
                if manual_result.stderr:
                    print_colored(f"详细错误信息:\n{manual_result.stderr}", Color.RED)
                if manual_result.stdout:
                    print_colored(f"输出信息:\n{manual_result.stdout}", Color.CYAN)
                    
                return False
                
        except subprocess.TimeoutExpired:
            print_colored("⚠️ 后端启动超时，但可能仍在启动中", Color.YELLOW)
            return True
        except Exception as e:
            print_colored(f"❌ 后端服务启动异常: {e}", Color.RED)
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print_colored("🚀 启动前端服务...", Color.YELLOW)
        
        try:
            self.frontend_process = subprocess.Popen([
                "npm", "run", "dev"
            ], cwd=self.frontend_dir)
            
            # 等待一下让前端启动
            time.sleep(2)
            
            # 检查进程是否还在运行
            if self.frontend_process.poll() is None:
                print_colored("✅ 前端服务启动成功 (http://localhost:3015)", Color.GREEN)
                return True
            else:
                print_colored("❌ 前端服务启动失败", Color.RED)
                return False
                
        except Exception as e:
            print_colored(f"❌ 前端服务启动异常: {e}", Color.RED)
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        def monitor_backend():
            if self.backend_process:
                self.backend_process.wait()
                print_colored("⚠️  后端服务已停止", Color.YELLOW)
        
        def monitor_frontend():
            if self.frontend_process:
                self.frontend_process.wait()
                print_colored("⚠️  前端服务已停止", Color.YELLOW)
        
        # 启动监控线程
        if self.backend_process:
            threading.Thread(target=monitor_backend, daemon=True).start()
        if self.frontend_process:
            threading.Thread(target=monitor_frontend, daemon=True).start()
    
    def cleanup(self):
        """清理进程"""
        print_colored("\n🛑 正在停止服务...", Color.YELLOW)
        
        if self.frontend_process and self.frontend_process.poll() is None:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print_colored("✅ 前端服务已停止", Color.GREEN)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print_colored("⚠️  强制停止前端服务", Color.YELLOW)
            except Exception as e:
                print_colored(f"❌ 停止前端服务失败: {e}", Color.RED)
        
        if self.backend_process and self.backend_process.poll() is None:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print_colored("✅ 后端服务已停止", Color.GREEN)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print_colored("⚠️  强制停止后端服务", Color.YELLOW)
            except Exception as e:
                print_colored(f"❌ 停止后端服务失败: {e}", Color.RED)
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print_colored(f"\n收到信号 {signum}，正在退出...", Color.YELLOW)
        self.cleanup()
        sys.exit(0)
    
    def run(self):
        """主运行方法"""
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            print_banner()
            
            # 检查前置条件
            if not self.check_prerequisites():
                return False
            
            # 安装依赖
            if not self.install_backend_dependencies():
                return False
                
            if not self.install_frontend_dependencies():
                return False
            
            # 启动服务
            if not self.start_backend():
                return False
                
            if not self.start_frontend():
                self.cleanup()
                return False
            
            # 显示启动成功信息
            print_colored("\n" + "="*60, Color.GREEN)
            print_colored("🎉 BiliNote 启动成功！", Color.GREEN)
            print_colored("📍 后端服务: http://localhost:8000", Color.CYAN)
            print_colored("📍 前端服务: http://localhost:3015", Color.CYAN)
            print_colored("按 Ctrl+C 停止所有服务", Color.YELLOW)
            print_colored("="*60, Color.GREEN)
            
            # 监控进程
            self.monitor_processes()
            
            # 等待用户中断
            try:
                while True:
                    time.sleep(1)
                    # 检查进程是否还在运行
                    if (self.backend_process and self.backend_process.poll() is not None) or \
                       (self.frontend_process and self.frontend_process.poll() is not None):
                        print_colored("⚠️  检测到服务异常停止", Color.YELLOW)
                        break
            except KeyboardInterrupt:
                pass
            
            return True
            
        except Exception as e:
            print_colored(f"❌ 启动过程中发生异常: {e}", Color.RED)
            return False
        finally:
            self.cleanup()

def main():
    """主函数"""
    starter = ProjectStarter()
    success = starter.run()
    
    if not success:
        print_colored("❌ 启动失败", Color.RED)
        sys.exit(1)
    
    print_colored("👋 再见！", Color.CYAN)

if __name__ == "__main__":
    main()