---
description: 
globs: 
alwaysApply: true
---
# BiliNote 项目规则

## 项目架构
- 后端：FastAPI (Python) - `backend/` 目录
- 前端：Vite + React (TypeScript) - `BillNote_frontend/` 目录

## 启动命令

### 一键启动（推荐）
```bash
python start.py
```
- 自动启动前后端服务
- 自动检查并安装依赖
- 提供彩色终端输出和进程监控
- 支持 Ctrl+C 优雅停止所有服务

### 手动启动

#### 后端启动
```bash
cd backend
pip install -r requirements.txt
python main.py
```
- 运行端口：http://localhost:8000

#### 前端启动
```bash
cd BillNote_frontend
npm install
npm run dev
```
- 运行端口：http://localhost:3015 或 http://localhost:3016

## 重要依赖版本
- ctranslate2==4.6.0 (已修复版本冲突)

## 开发注意事项
- 前端如果3015端口被占用，会自动切换到下一个可用端口
- 后端API代理配置在前端的vite.config.ts中
- 项目使用Python 3.13和Node.js
- start.py 提供完整的环境检查和依赖管理

## 常见问题
- 如果遇到ctranslate2版本错误，确保使用4.6.0版本
- 如果前端代理错误，确保后端服务已启动
- 使用 start.py 可以避免大部分环境配置问题
